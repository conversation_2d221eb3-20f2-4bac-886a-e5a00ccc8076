import { Link, useLocation } from "react-router-dom";
import { trackClick, trackCustomEvent } from "../../utils/analytics.js";
import { getUtmCookie } from "../../utils/cookieHelpers.js";
import { useState } from "react";
import AppLogo from "../../assets/app-logo.svg";

const Logo = () => {
  const handleLogoClick = () => {
    trackClick("Header Logo", {
      destination: "https://pinnaclefundingco.com",
    });
  };

  return (
    <Link
      to={"https://pinnaclefundingco.com"}
      className="text-xl font-bold text-blue-600"
      onClick={handleLogoClick}
    >
      <img
        src={AppLogo}
        alt="Pinnacle Funding"
        className="w-28 sm:w-40 lg:w-60 max-h-10 sm:max-h-16 py-3 min-w-0"
      />
    </Link>
  );
};

const PhoneNumber = () => {
  const handlePhoneClick = () => {
    trackClick("Phone", { phone: "+****************" });
  };

  return (
    <div className="flex items-center group">
      <div className="flex items-center transition-all duration-300 ease-in-out group-hover:scale-105">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 mr-2 text-gray-600 transition-colors duration-300 group-hover:text-blue-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
          />
        </svg>
        <a
          href="tel:3476948180"
          className="font-medium text-gray-700 transition-colors duration-300 group-hover:text-blue-600 text-base lg:text-lg"
          onClick={handlePhoneClick}
        >
          <span className="sm:inline">+****************</span>
        </a>
      </div>
    </div>
  );
};

const ApplyNowButton = () => {
  const handleApplyNowClick = () => {
    // Smooth scroll to the form section
    const formSection = document.querySelector(
      "#prequalify-form-scroll-target"
    );
    if (formSection) {
      formSection.scrollIntoView({
        behavior: "smooth",
        block: "start",
        inline: "center",
      });
    }

    trackCustomEvent("apply_now_click", true);
  };

  return (
    <button
      onClick={handleApplyNowClick}
      className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-3 sm:py-3 sm:px-6 rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm sm:text-base whitespace-nowrap"
    >
      Apply Now
    </button>
  );
};

export const Header = () => {
  const location = useLocation();
  const utmAf = getUtmCookie("utm_af");
  const [showPartnerLogo, setShowPartnerLogo] = useState(utmAf ? true : false);
  const [partnerLogoLoaded, setPartnerLogoLoaded] = useState(false);

  const showApplyNowButton = location.pathname === "/";

  const partnerLogoSrc = `https://static.pinnaclefunding.com/affiliates/${utmAf}.svg`;

  return (
    <nav className="bg-white shadow-lg">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
        <div className="flex justify-between h-16 sm:h-20 lg:h-24">
          <div className="flex items-center space-x-2 sm:space-x-4">
            <Logo />
            {showPartnerLogo && partnerLogoLoaded && (
              <>
                <div className="h-6 sm:h-10 w-px bg-gray-400"></div>
              </>
            )}
            {showPartnerLogo && (
              <img
                onLoad={() => setPartnerLogoLoaded(true)}
                onError={() => setShowPartnerLogo(false)}
                src={partnerLogoSrc}
                alt={utmAf}
                className={`${
                  partnerLogoLoaded ? "" : "hidden"
                } w-20 sm:w-36 max-h-8 sm:max-h-16 min-w-0`}
              />
            )}
          </div>
          <div className="flex items-center space-x-1 sm:space-x-4">
            {showApplyNowButton && <ApplyNowButton />}
            <PhoneNumber />
          </div>
        </div>
      </div>
    </nav>
  );
};
